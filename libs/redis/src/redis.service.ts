import { Inject, Injectable } from '@nestjs/common';
import { Cluster } from 'ioredis';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';

@Injectable()
export class RedisService {
    constructor(
        @Inject('REDIS_CLUSTER') private readonly redisClient: Cluster,
        @InjectPinoLogger(RedisService.name)
        private readonly logger: PinoLogger,
    ) {}

    getClient(): Cluster {
        return this.redisClient;
    }

    async acquireLock(lockKey: string, lockValue: string, ttlMs: number = 20000): Promise<boolean> {
        try {
            const result = await this.redisClient.set(lockKey, lockValue, 'EX', Math.ceil(ttlMs / 1000), 'NX');

            if (result === 'OK') {
                this.logger.debug(`Lock acquired: ${lockKey}`);
                return true;
            }

            return false;
        } catch (error) {
            this.logger.warn(`Error acquiring lock: ${error.message}`, error.stack);
            return false;
        }
    }

    async retryAcquireLock(
        lockKey: string,
        lockValue: string,
        ttlMs: number = 30000,
        retryDelayMs: number = 200,
        maxRetries: number = 5,
    ): Promise<boolean> {
        let retries = 0;

        while (retries < maxRetries) {
            try {
                const result = await this.redisClient.set(lockKey, lockValue, 'EX', Math.ceil(ttlMs / 1000), 'NX');

                if (result === 'OK') {
                    this.logger.debug(`Lock acquired: ${lockKey}`);
                    return true;
                }

                // If we couldn't acquire the lock, wait before retrying
                retries++;
                if (retries < maxRetries) {
                    await new Promise((resolve) => setTimeout(resolve, retryDelayMs));
                }
            } catch (error) {
                this.logger.warn({ err: error.stack }, `Error acquiring lock: ${error.message}`);
                return false;
            }
        }

        this.logger.debug(`Failed to acquire lock after ${maxRetries} attempts: ${lockKey}`);
        return false;
    }

    async releaseLock(lockKey: string, lockValue: string): Promise<boolean> {
        const script = `
      if redis.call("get", KEYS[1]) == ARGV[1] then
        return redis.call("del", KEYS[1])
      else
        return 0
      end
    `;

        try {
            const result = await this.redisClient.eval(script, 1, lockKey, lockValue);
            const released = result === 1;
            if (released) {
                this.logger.debug(`Lock released: ${lockKey}`);
            } else {
                this.logger.debug(`Lock not released (not owner or already expired): ${lockKey}`);
            }
            return released;
        } catch (error) {
            this.logger.warn({ err: error.stack }, `Error releasing lock: ${error.message}`);
            return false;
        }
    }

    async checkLock(lockKey: string): Promise<boolean> {
        try {
            const exists = await this.redisClient.exists(lockKey);
            return exists === 1;
        } catch (error) {
            this.logger.error({ err: error.stack }, `Error checking lock: ${error.message}`);
            return false;
        }
    }

    async getCachedValue(key: string): Promise<string | null> {
        try {
            return this.redisClient.get(key);
        } catch (error) {
            this.logger.error({ err: error.stack }, `Error getting cached value: ${error.message}`);
            return null;
        }
    }

    async setCachedValue(key: string, value: string, ttlMs: number = 60000): Promise<boolean> {
        try {
            await this.redisClient.set(key, value, 'PX', ttlMs);
            return true;
        } catch (error) {
            this.logger.error({ err: error.stack }, `Error setting cached value: ${error.message}`);
            return false;
        }
    }

    async removeCached(key: string): Promise<boolean> {
        try {
            const result = await this.redisClient.del(key);
            return result > 0;
        } catch (error) {
            this.logger.error({ err: error.stack }, `Error removing cached value: ${error.message}`);
            return false;
        }
    }
}
