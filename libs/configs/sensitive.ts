import * as path from 'path';
import * as fs from 'fs';

export const enum SENSITIVE_CONFIG {
    HYPER_LIQUID = 'hyperliquid',
}

export interface IHyperLiquidConfig {
    builderAddress: string;
    referralCode: string;
    maxFeeRate: number;
}

export function loadSensitiveConfig<T>(name: string) {
    const stage = process.env.STAGE ?? 'prod';

    const rootPath = path.join(__dirname, '../../../../../sensitive_configs');
    const filePath = path.resolve(rootPath, `${name}.json`);
    if (!fs.existsSync(filePath)) {
        throw new Error(`Sensitive config file not found: ${filePath}`);
    }

    const sensitiveConfig = JSON.parse(fs.readFileSync(filePath, 'utf-8')) as Record<string, T>;

    if (!sensitiveConfig[stage]) {
        throw new Error(`Sensitive config for stage "${stage}" not found in ${name}.json`);
    }

    return sensitiveConfig[stage];
}
