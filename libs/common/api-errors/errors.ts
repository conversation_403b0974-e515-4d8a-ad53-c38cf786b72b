import { IApiError } from './index';
import { status } from '@grpc/grpc-js';

export const INVALID_SIGNATURE_ERROR: IApiError = {
    code: 'INVALID_SIGNATURE_ERROR',
    message: 'Invalid signature',
};

export const INVALID_REFRESH_TOKEN_ERROR: IApiError = {
    code: 'INVALID_REFRESH_TOKEN_ERROR',
    message: 'Invalid refresh token',
};

export const INVALID_AUTH_CODE_ERROR: IApiError = {
    code: 'INVALID_AUTH_CODE_ERROR',
    message: 'Invalid auth code',
};

export const INVALID_GOOGLE_LOGIN: IApiError = {
    code: 'INVALID_GOOGLE_LOGIN',
    message: 'Invalid Google login',
};

export const INVALID_APPLE_LOGIN: IApiError = {
    code: 'INVALID_APPLE_LOGIN',
    message: 'Invalid Apple login',
};

export const TOKEN_EXPIRED: IApiError = {
    code: 'TOKEN_EXPIRED',
    message: 'Token expired',
};

export const USER_NOT_FOUND_ERROR: IApiError = {
    code: 'USER_NOT_FOUND_ERROR',
    message: 'User not found',
};

export const USER_NOT_ALLOW: IApiError = {
    code: 'USER_NOT_ALLOW',
    message: 'User was not allowed',
};

export const CHAIN_TYPE_NOT_SUPPORTED: IApiError = {
    code: 'CHAIN_TYPE_NOT_SUPPORTED',
    message: 'chain type not supported',
};

export const INTERNAL_API_ERROR: IApiError = {
    code: 'INTERNAL_API_ERROR',
    message: 'internal api error',
};

export const INVALID_GRAPHQL_INPUT: IApiError = {
    code: 'INVALID_GRAPHQL_INPUT',
    message: 'input is invalid',
};

export const USER_NOT_FOUND: IApiError = {
    code: 'USER_NOT_FOUND',
    message: 'Invalid user',
};

export const DUPLICATE_REQUEST: IApiError = {
    code: 'DUPLICATE_REQUEST',
    message: "We don't allow request too many on this. Please retry later.",
};

export const INVALID_TELEGRAM_LOGIN: IApiError = {
    code: 'INVALID_TELEGRAM_LOGIN',
    message: 'Invalid telegram user authentication',
};

export const AGENT_OF_WALLET_NOT_AVAILABLE: IApiError = {
    code: 'AGENT_OF_WALLET_NOT_AVAILABLE',
    message: 'Agent of wallet is expired or not exists',
};

export const FAILED_TO_APPROVE_AGENT: IApiError = {
    code: 'FAILED_TO_APPROVE_AGENT',
    message: 'Sign approve agent failed',
};

export const TWO_FACTOR_IS_NOT_ENABLE: IApiError = {
    code: 'TWO_FACTOR_IS_NOT_ENABLE',
    message: '2FA setting is not enable',
};

export const TWO_FACTOR_IS_ENABLE: IApiError = {
    code: 'TWO_FACTOR_IS_ENABLE',
    message: '2FA setting is enable',
};

export const TWO_FACTOR_IS_NOT_SETUP: IApiError = {
    code: 'TWO_FACTOR_IS_NOT_SETUP',
    message: '2FA setting is not setup',
};

export const INVALID_OTP_CODE = {
    code: 'INVALID_OTP_CODE',
    message: 'Invalid OTP code',
};

export const OTP_INIT_FAILED: IApiError = {
    code: 'OTP_INIT_FAILED',
    message: 'Failed to initialize OTP authentication',
};

export const OTP_VERIFICATION_FAILED: IApiError = {
    code: 'OTP_VERIFICATION_FAILED',
    message: 'OTP verification failed',
};

export const OTP_EXPIRED: IApiError = {
    code: 'OTP_EXPIRED',
    message: 'OTP code has expired',
};

export const OTP_RATE_LIMITED: IApiError = {
    code: 'OTP_RATE_LIMITED',
    message: 'Too many OTP requests. Please try again later',
};

export const RATE_LIMIT_EXCEEDED: IApiError = {
    code: 'RATE_LIMIT_EXCEEDED',
    message: 'Too many requests from this IP address. Please try again later',
};

export const WALLET_NOT_FOUND: IApiError = {
    code: 'WALLET_NOT_FOUND',
    message: 'Wallet not found',
};

export const WALLET_ACCESS_DENIED: IApiError = {
    code: 'WALLET_ACCESS_DENIED',
    message: 'You do not have permission to modify this wallet',
};

export const INVALID_WALLET_NAME: IApiError = {
    code: 'INVALID_WALLET_NAME',
    message: 'Wallet name is invalid',
};

export const WALLET_NAME_ALREADY_EXISTS: IApiError = {
    code: 'WALLET_NAME_ALREADY_EXISTS',
    message: 'Wallet name already exists for this user',
};

export const EMAIL_OTP_RATE_LIMITED: IApiError = {
    code: 'EMAIL_OTP_RATE_LIMITED',
    message: 'Too many email OTP requests from this IP. Please wait 15 minutes before trying again',
};

export const LOGIN_RATE_LIMITED: IApiError = {
    code: 'LOGIN_RATE_LIMITED',
    message: 'Too many login attempts from this IP. Please wait 15 minutes before trying again',
};

export const EXPORT_RATE_LIMITED: IApiError = {
    code: 'EXPORT_RATE_LIMITED',
    message: 'Too many export requests from this IP. Please wait 15 minutes before trying again',
};

export const EMAIL_RATE_LIMITED: IApiError = {
    code: 'EMAIL_RATE_LIMITED',
    message: 'Too many requests for this email. Please wait 1 minute before trying again',
};

export const FINGERPRINT_RATE_LIMITED: IApiError = {
    code: 'FINGERPRINT_RATE_LIMITED',
    message: 'Too many requests from this device. Please wait 1 minute before trying again',
};

export const OTP_VERIFICATION_THROTTLED: IApiError = {
    code: 'OTP_VERIFICATION_THROTTLED',
    message: 'Too many failed OTP verification attempts. Please wait {waitTime} before trying again',
};

export const INVALID_EMAIL_FORMAT: IApiError = {
    code: 'INVALID_EMAIL_FORMAT',
    message: 'Invalid email format',
};

export const EXPORT_PASSPHRASE_ALREADY_CREATED: IApiError = {
    code: 'EXPORT_PASSPHRASE_ALREADY_CREATED',
    message: 'Export passphrase already created',
};

export const EMBEDDED_WALLET_NOT_FOUND: IApiError = {
    code: 'EMBEDDED_WALLET_NOT_FOUND',
    message: 'Embedded wallet not found',
};

export const TURNKEY_INVALID_ACTIVITY: IApiError = {
    code: 'TURNKEY_INVALID_ACTIVITY',
    message: 'Invalid activity type for Turnkey',
};

export const EXPORT_PASSPHRASE_FAILED: IApiError = {
    code: 'EXPORT_PASSPHRASE_FAILED',
    message: 'Failed to export passphrase',
};

export const EXPORT_PRIVATE_KEY_FAILED: IApiError = {
    code: 'EXPORT_PRIVATE_KEY_FAILED',
    message: 'Failed to export private key',
};

export const INVALID_USER_VERIFICATION: IApiError = {
    code: 'INVALID_USER_VERIFICATION',
    message: 'Invalid user verification',
};

export const SOLANA_WALLET_LIMIT_REACHED: IApiError = {
    code: 'SOLANA_WALLET_LIMIT_REACHED',
    message: 'Solana wallet limit reached',
};

export const FAILED_TO_CREATE_WALLET: IApiError = {
    code: 'FAILED_TO_CREATE_WALLET',
    message: 'Failed to create wallet',
};

export const FAILED_TO_WITHDRAW_TRANSACTION: IApiError = {
    code: 'FAILED_TO_WITHDRAW_TRANSACTION',
    message: 'Failed to withdraw transaction',
};

export const INVALID_WITHDRAW_TRANSACTION: IApiError = {
    code: 'INVALID_WITHDRAW_TRANSACTION',
    message: 'Invalid withdraw transaction',
};

export const ACCOUNT_NOT_DELETED: IApiError = {
    code: 'ACCOUNT_NOT_DELETED',
    message: 'Account is not deleted and cannot be reactivated',
};

export const ACCOUNT_DELETED: IApiError = {
    code: 'ACCOUNT_DELETED',
    message: 'Account has been deleted. Please reactivate your account first.',
};

export const INVALID_CORS: IApiError = {
    code: 'INVALID_CORS',
    message: 'Domain is not allowed by CORS policy',
};

export const GRPC_EXCEPTION = {
    INTERNAL_SERVER_ERROR: {
        code: status.UNKNOWN,
        message: 'INTERNAL_SERVER_ERROR',
    },
    PLEASE_DEPOSIT_FIRST: {
        code: status.INVALID_ARGUMENT,
        message: 'PLEASE_DEPOSIT_FIRST',
    },
    AGENT_WALLET_ALREADY_EXISTS: {
        code: status.ALREADY_EXISTS,
        message: 'AGENT_WALLET_ALREADY_EXISTS',
    },
    FAILED_TO_APPROVE_AGENT: {
        code: status.UNKNOWN,
        message: 'FAILED_TO_APPROVE_AGENT',
    },
    FAILED_TO_GENERATE: {
        code: status.UNKNOWN,
        message: 'FAILED_TO_GENERATE',
    },
    AGENT_OF_WALLET_NOT_AVAILABLE: {
        code: status.FAILED_PRECONDITION,
        message: 'AGENT_OF_WALLET_NOT_AVAILABLE',
    },
    USER_NOT_FOUND: {
        code: status.FAILED_PRECONDITION,
        message: 'USER_NOT_FOUND',
    },
    USER_WALLET_NOT_FOUND: {
        code: status.FAILED_PRECONDITION,
        message: 'USER_WALLET_NOT_FOUND',
    },
};
