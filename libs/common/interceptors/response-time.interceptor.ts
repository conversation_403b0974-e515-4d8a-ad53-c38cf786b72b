import { CallHandler, ContextType, ExecutionContext, Injectable, Logger, NestInterceptor } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import { catchError, Observable, tap, throwError } from 'rxjs';

const REQUIRED_RESPONSE_TIME = 200; // in ms

@Injectable()
export class ResponseTimeInterceptor implements NestInterceptor {
    constructor(
        @InjectPinoLogger(ResponseTimeInterceptor.name)
        private readonly logger: PinoLogger,
    ) {}

    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        const startTime = Date.now();

        return next.handle().pipe(
            tap(() => {
                setTimeout(() => this.shouldAlertRequest(startTime, context), 0);
            }),
            catchError((err) => {
                setTimeout(() => this.shouldAlertRequest(startTime, context), 0);
                return throwError(() => err);
            }),
        );
    }

    private shouldAlertRequest(startTime: number, context: ExecutionContext) {
        const responseTime = Date.now() - startTime;

        if (responseTime > REQUIRED_RESPONSE_TIME) {
            const protocol = context.getType();
            const request = {
                protocol,
            } as any;

            switch (protocol) {
                case 'http': {
                    const reqHttp = context.switchToHttp().getRequest();
                    request.url = reqHttp.url;
                    request.method = reqHttp.method;
                    break;
                }
                case 'graphql' as ContextType: {
                    const ctx = GqlExecutionContext.create(context);
                    const reqGraphql = ctx.getInfo();
                    request.query = `${reqGraphql.parentType.name}.${reqGraphql.fieldName}`;
                    break;
                }
                case 'rpc' as ContextType: {
                    const ctx = context.switchToRpc();
                    request.handler = context?.getHandler()?.name;
                    break;
                }
                default:
                    break;
            }

            this.logger.warn(request, `[Alert Response] Slow response time ${responseTime} ms`);
        }
    }
}
