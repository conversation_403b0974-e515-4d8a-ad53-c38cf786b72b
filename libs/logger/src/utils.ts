import pino, { Level } from 'pino';
import { Options } from 'pino-http';

export function loggerOptions(environment: string, level: Level = 'debug', context?: string): Options {
    const isLocal = environment === 'local';
    if (!['fatal', 'error', 'warn', 'info', 'debug', 'trace'].includes(level)) {
        level = 'debug';
    }

    return {
        logger: pino({
            formatters: {
                level: (label) => {
                    return { level: label };
                },
                log(object): Record<string, unknown> {
                    const censoredData = dashingObject(object);

                    if (object instanceof Array) {
                        return {
                            app: context,
                            data: censoredData,
                        };
                    }

                    if (Object.keys(object).length) {
                        return {
                            app: context,
                            data: censoredData,
                        };
                    }

                    return censoredData as Record<string, unknown>;
                },
            },
            base: undefined,
            timestamp: () => `,"time": "${new Date(Date.now()).toISOString()}"`,
            transport: isLocal
                ? {
                      target: 'pino-pretty',
                      options: {
                          colorize: true,
                          ignore: 'pid,hostname',
                          singleLine: true,
                      },
                  }
                : undefined,
            level: level,
        }),
        autoLogging: false,
        serializers: {
            req: (req) => ({
                method: req.method,
                url: req.url,
            }),
        },
    };
}

export const censorProperties = [
    'privateKey',
    'private_key',
    'password',
    'secret',
    'encrypted_private_key',
    'encryptPrivateKey',
    'key',
    'otp',
    'email',
    'accessToken',
    'access_token',
    'refreshToken',
    'refresh_token',
    'token',
];

export function dashingObject(
    obj: Record<string, unknown> | Record<string, unknown>[],
): Record<string, unknown> | Record<string, unknown>[] {
    if (obj instanceof Array) {
        return obj.map((item) => dashingObject(item) as Record<string, unknown>);
    }

    const newObj: Record<string, unknown> = {};
    for (const key in obj) {
        if (censorProperties.includes(key)) {
            newObj[key] = '********';
        } else if (typeof obj[key] === 'object') {
            newObj[key] = dashingObject(obj[key] as Record<string, unknown>);
        } else if (isEvmPrivateKey(obj[key] as string)) {
            newObj[key] = '********';
        } else if (isSolanaPrivateKey(obj[key] as string)) {
            newObj[key] = '********';
        } else if (isEncryptedPrivateKey(obj[key] as string)) {
            newObj[key] = '********';
        } else {
            newObj[key] = obj[key];
        }
    }

    if (obj instanceof Error) {
        for (const key of ['message', 'stack']) {
            if (typeof obj[key] === 'object') {
                newObj[key] = dashingObject(obj[key] as Record<string, unknown>);
            } else if (isEvmPrivateKey(obj[key] as string)) {
                newObj[key] = '********';
            } else if (isSolanaPrivateKey(obj[key] as string)) {
                newObj[key] = '********';
            } else if (isEncryptedPrivateKey(obj[key] as string)) {
                newObj[key] = '********';
            } else {
                newObj[key] = obj[key];
            }
        }
    }

    return newObj;
}

function randomString(length = 6) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

export function randomUniqueWalletDefault() {
    return `Wallet ${randomString(6)}`;
}

function isEvmPrivateKey(log: string) {
    try {
        const evmPrivateKeyPattern = /0x[a-fA-F0-9]{64}/;
        return log.includes('private key') || evmPrivateKeyPattern.test(log);
    } catch (error) {
        return false;
    }
}

function isSolanaPrivateKey(log: string) {
    try {
        const solanaPrivateKeyPattern = /[1-9A-HJ-NP-Za-km-z]{43,44}/;
        return solanaPrivateKeyPattern.test(log);
    } catch (error) {
        return false;
    }
}

function isEncryptedPrivateKey(log: string) {
    try {
        const encryptedPrivateKeyPattern = /[A-Za-z0-9+/]{100,}={1,2}/;
        return encryptedPrivateKeyPattern.test(log);
    } catch (error) {
        return false;
    }
}
