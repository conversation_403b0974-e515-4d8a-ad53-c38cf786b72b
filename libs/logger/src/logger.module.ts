import { DynamicModule, Module } from '@nestjs/common';
import { LoggerService } from './logger.service';
import { LoggerModule as PinoLoggerModule, PinoLogger } from 'nestjs-pino';
import { Level } from 'pino';
import { loggerOptions } from './utils';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { appConfig } from 'libs/configs';

@Module({})
export class LoggerModule {
    static forRootAsync(context?: string): DynamicModule {
        return {
            module: LoggerModule,
            global: true,
            imports: [
                PinoLoggerModule.forRootAsync({
                    imports: [ConfigModule],
                    useFactory: () => ({
                        pinoHttp: loggerOptions(process.env.STAGE || 'prod', appConfig.LOG_LEVEL as Level, context),
                    }),
                    inject: [ConfigService],
                }),
            ],
            providers: [LoggerService, PinoLogger],
            exports: [LoggerService, PinoLogger],
        };
    }
}
