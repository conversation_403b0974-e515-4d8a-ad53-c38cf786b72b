export const retryCaller = async <T>(fn: () => Promise<T>, retries = 5, delay = 1000): Promise<T> => {
    for (let i = 0; i < retries; i++) {
        try {
            return await fn();
        } catch (error) {
            if (i === retries - 1) {
                throw error;
            }
            await new Promise((resolve) => setTimeout(resolve, delay));
        }
    }

    throw new Error('Retry failed');
};
