import { Inject, Injectable, OnModuleInit } from '@nestjs/common';
import { appConfig } from 'libs/configs';
import { JetStreamClient, JetStreamManager, NatsConnection, RetentionPolicy, StorageType } from 'nats';
import { InjectPinoLogger } from 'nestjs-pino';

@Injectable()
export class NatsService implements OnModuleInit {
    private js: JetStreamClient;
    constructor(
        @Inject('NATS_CLIENT') private readonly natsClient: NatsConnection,
        @InjectPinoLogger(NatsService.name) private readonly logger: any,
    ) {
        this.js = natsClient.jetstream();
    }
    async onModuleInit() {
        const jsm: JetStreamManager = await this.natsClient.jetstreamManager();

        this.createStreamIfNotExists(jsm, appConfig.NATS_USER_STREAM, ['user.>']);
        this.createStreamIfNotExists(jsm, appConfig.NATS_DEX_USER_STREAM, ['dex.user.>']);
        this.createStreamIfNotExists(jsm, appConfig.NATS_DEX_USER_INFO_STREAM, [appConfig.NATS_SUBJECT_DEX_USER_SYNC]);
    }

    async publish<T>(subject: string, data: T) {
        // add try-catch to prevent breaking main business logic
        try {
            await this.js.publish(subject, JSON.stringify(data));
        } catch (error) {
            this.logger.error(error, `Failed to publish message to subject ${subject}:`);
        }
    }

    getClient() {
        return this.natsClient;
    }

    async createStreamIfNotExists(jsm: JetStreamManager, streamName: string, subjects: string[]) {
        try {
            await jsm.streams.info(streamName);
            this.logger.info(`✅ Stream ${streamName} already exists`);
            return true;
        } catch (err) {
            this.logger.info(`🛠️ Stream ${streamName} not found, creating...`);
            try {
                await jsm.streams.add({
                    name: streamName,
                    subjects: subjects,
                    retention: RetentionPolicy.Limits,
                    storage: StorageType.File,
                    max_age: 2 * 24 * 60 * 60 * 1_000_000_000, // max 2 days
                });
                this.logger.info(`✅ Stream ${streamName} created`);

                return true;
            } catch (error) {
                this.logger.error(error, `Failed to create stream ${streamName}:`);
                return false;
            }
        }
    }
}
