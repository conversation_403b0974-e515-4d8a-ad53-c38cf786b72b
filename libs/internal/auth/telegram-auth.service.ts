import { Injectable } from '@nestjs/common';
import { UsersService } from '../users/users.service';
import { CachingService } from '../caching/caching.service';
import { ApiError } from '../../common/api-errors';
import { GRPC_EXCEPTION, INVALID_AUTH_CODE_ERROR, USER_NOT_FOUND_ERROR } from '../../common/api-errors/errors';
import { JwtService } from '@nestjs/jwt';
import { appConfig } from '../../configs';
import { LoginDTO } from './dto/auth.dto';
import { InjectBot } from 'nestjs-telegraf';
import { Telegraf } from 'telegraf';
import { JwtClaim } from './auth.vo';
import { AUTH_AGENT } from './auth.constant';
import { RpcException } from '@nestjs/microservices';
import { formatText } from 'libs/common/utils/helpers2';
import { translate } from 'libs/i18n/i18n.helper';
import { I18nService } from 'nestjs-i18n';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { UserLoginEvent } from '../events/user-login.event';

@Injectable()
export class TelegramAuthService {
    constructor(
        private userService: UsersService,
        private cachingService: CachingService,
        private jwtService: JwtService,
        private readonly i18nService: I18nService,
        @InjectBot(appConfig.TELEGRAM_BOT_AUTH_NAME) private bot: Telegraf,
        private readonly eventEmitter: EventEmitter2,
    ) {}

    async login(userId: string, code: string): Promise<LoginDTO> {
        // bypass for automated testing
        if (
            appConfig.STAGE === 'unstable' &&
            userId === '0195c76a-7031-7755-b4da-bb4c9b6c4abd' &&
            code === '2a27c8b4-9b56-4fb8-a889-b5e64eabbd6d'
        ) {
            const payload: JwtClaim = {
                sub: userId,
                iss: AUTH_AGENT,
            };

            const loginResult = {
                accessToken: await this.jwtService.signAsync(payload),
                refreshToken: await this.jwtService.signAsync(payload, {
                    secret: appConfig.JWT_REFRESH_SECRET,
                    expiresIn: appConfig.REFRESH_TOKEN_EXPIRES_IN,
                }),
                userId,
            };

            // Emit login event for test bypass
            const loginEvent = new UserLoginEvent(userId, 'telegram', new Date());

            this.eventEmitter.emit('user.login', loginEvent);

            return loginResult;
        }
        if (appConfig.STAGE != 'dev') {
            // TODO: remove on production
            const currentCode = await this.cachingService.getAuthCode(userId);
            if (!currentCode) {
                throw new ApiError(INVALID_AUTH_CODE_ERROR);
            }
            if (currentCode != code) {
                throw new ApiError(INVALID_AUTH_CODE_ERROR);
            }
        }

        const user = await this.userService.getUserById(userId);
        if (!user) {
            throw new ApiError(USER_NOT_FOUND_ERROR);
        }

        if (user.telegramChatId) {
            setTimeout(() => void this.sendTelegramNewLoginMessage(user.telegramChatId, user.language), 0);
        }
        const payload: JwtClaim = {
            sub: user.id,
            iss: AUTH_AGENT,
        };
        const loginDto = {
            accessToken: await this.jwtService.signAsync(payload),
            refreshToken: await this.jwtService.signAsync(payload, {
                secret: appConfig.JWT_REFRESH_SECRET,
                expiresIn: appConfig.REFRESH_TOKEN_EXPIRES_IN,
            }),
            userId: user.id,
            deletedAt: user.deletedAt,
        };

        // Emit login event for async processing
        const loginEvent = new UserLoginEvent(user.id, 'telegram', new Date());

        this.eventEmitter.emit('user.login', loginEvent);

        return loginDto;
    }

    async getAccessToken(telegramId: string, userId: string): Promise<LoginDTO> {
        const user = await this.userService.getUserInfoByTelegram({
            telegramId,
            userId,
        });

        if (!user) {
            throw new RpcException(GRPC_EXCEPTION.USER_NOT_FOUND);
        }

        const payload: JwtClaim = {
            sub: user.id,
            iss: AUTH_AGENT,
        };

        const accessToken = await this.jwtService.signAsync(payload);

        return {
            accessToken: accessToken,
            userId: user.id,
            refreshToken: '', // unused, add generate if need
            deletedAt: undefined, // UserInfoResponse doesn't have deletedAt field
        };
    }

    async getAuthCode(telegramId: string, userId: string): Promise<{ code: string; userId: string }> {
        const user = await this.userService.getUserInfoByTelegram({
            telegramId,
            userId,
        });

        if (!user) {
            throw new RpcException(GRPC_EXCEPTION.USER_NOT_FOUND);
        }

        const codeCache = await this.cachingService.getAuthCode(user.id);
        if (codeCache) {
            return { code: codeCache, userId: user.id };
        }
        const code = await this.cachingService.generateAuthCode(user.id);
        if (!code) {
            throw new RpcException(GRPC_EXCEPTION.FAILED_TO_GENERATE);
        }
        return { code: code, userId: user.id };
    }

    async sendTelegramNewLoginMessage(telegramChatId?: number, language?: string) {
        const text = this.i18nService.translate('validation.common.text.loginMessage', {
            lang: language,
        });
        return this.bot.telegram.sendMessage(
            Number(telegramChatId),
            // `🌌 *Welcome to Xbit*\n⚡ Fast on\\-chain, click to trade\\.\n` +
            //     `📍 Auto sell with stop loss\\/take profit\\.\n` +
            //     `📊 Track smart money & KOLs with alerts\\.\n` +
            //     `📈 Real\\-time wallet P&L analysis\\.\n` +
            //     `⚠️ Detect insider trading\\/rug risks\\.\n\n` +
            //     `🔗 *Visit:* [Xbit AI]$({appConfig.LANDING_PAGE_URL}))`
            formatText(text, appConfig.REFERRAL_ADDRESS),
            {
                parse_mode: 'HTML',
                reply_markup: {
                    inline_keyboard: [[{ text: '⚡ Trading Bot ↗', url: appConfig.REFERRAL_ADDRESS }]],
                },
            },
        );
    }
}
