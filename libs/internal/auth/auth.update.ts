import { Update, Ctx, Start, Hears, Sender, Action, Settings, InjectBot } from 'nestjs-telegraf';
import { Context, Markup, Telegraf } from 'telegraf';
import { UsersService } from '../users/users.service';
import { CachingService } from '../caching/caching.service';
import { TelegramUserDTO } from '../users/dto/telegram-user.dto';
import { appConfig } from '../../configs';
import { ChainType, UserManagedWallet } from '../users/entities/user-managed-wallet.entity';
import { EntityManager } from '@mikro-orm/core';
import { getArbBalance, getEvmBalance, getSolanaBalance } from '../users/wallet.utils';
import { Injectable, OnModuleInit } from '@nestjs/common';
import { isLanguageFileExists, translate } from 'libs/i18n/i18n.helper';
import { formatText } from 'libs/common/utils/helpers2';
import { I18n, I18nService } from 'nestjs-i18n';

@Injectable()
export class AuthUpdate implements OnModuleInit {
    constructor(
        private userService: UsersService,
        private cachingService: CachingService,
        private readonly em: EntityManager,
        private readonly i18nService: I18nService,
        @InjectBot(appConfig.TELEGRAM_BOT_AUTH_NAME) private readonly bot: Telegraf,
    ) {
        this.bot.command('start', async (ctx) => {
            this.start(ctx);
        });
    }

    onModuleInit() {
        this.bot.telegram.setMyCommands([{ command: 'start', description: 'Start' }]);
    }
    // @Start()
    async start(
        @Ctx() ctx: Context,
        // @Sender('id') id: string,
        // @Sender('username') username: string,
        // @Sender('first_name') firstName: string,
        // @Sender('last_name') lastName: string,
    ) {
        let language_key;
        let referral_code;
        let domain;
        if (ctx.message && 'text' in ctx.message) {
            const text = ctx.message.text;
            const parts = text.split(' ');
            const payload = parts.length > 1 ? parts[1] : null;
            if (payload) {
                const [language, referralCode, _domain] = payload.split('_');
                if (language) language_key = Buffer.from(language, 'base64').toString('utf-8');
                if (referralCode) referral_code = Buffer.from(referralCode, 'base64').toString('utf-8');
                if (_domain) domain = Buffer.from(_domain, 'base64').toString('utf-8');
            }
        }

        const sender = ctx.from;
        const userDto: TelegramUserDTO = {
            // id,
            // username,
            // firstName,
            // lastName,
            id: sender?.id.toString() || '',
            username: sender?.username || '',
            firstName: sender?.first_name || '',
            lastName: sender?.last_name || '',
            chatId: ctx.chat?.id,
        };

        const { user, isNewUser } = await this.userService.getOrCreateUserByTelegramAccount(userDto);
        const code = await this.cachingService.generateAuthCode(user.id);
        const wallets = await this.em.fork().find(UserManagedWallet, { user: user });
        const solana: any = { balance: 0 };
        const arb: any = { balance: 0 };
        const evm: any = { balance: 0 };
        for (const wallet of wallets) {
            if (wallet.chain == ChainType.EVM) {
                evm.walletAddress = wallet.walletAddress;
            }
            if (wallet.chain == ChainType.SOLANA) {
                solana.walletAddress = wallet.walletAddress;
            }
            if (wallet.chain == ChainType.ARB) {
                arb.walletAddress = wallet.walletAddress;
            }
        }
        if (!isNewUser) {
            solana.balance = await getSolanaBalance(solana.walletAddress);
            evm.balance = await getEvmBalance(evm.walletAddress);
            arb.balance = await getArbBalance(evm.walletAddress);
        }
        if (language_key) {
            if (!isLanguageFileExists(language_key)) language_key = user.language;
            else await this.userService.updateUserLanguage(user.id, language_key);
        } else language_key = user.language;

        const urlLogin = `${domain ? `https://${domain}/telegram-auth` : appConfig.TELEGRAM_LOGIN_URL}?code=${code}&user_id=${user.id}${referral_code ? `&r=${referral_code}` : ''}`;
        const textlogin = this.i18nService.t('validation.common.text.textlogin', { lang: language_key });
        const solText = this.i18nService.t('validation.common.text.sol', {
            lang: language_key,
        });
        const ethText = this.i18nService.translate('validation.common.text.eth', {
            lang: language_key,
        });

        const arbText = this.i18nService.translate('validation.common.text.arb', {
            lang: language_key,
        });

        const refLink = this.i18nService.translate('validation.common.reflink', { lang: language_key });
        const tapToCopy = this.i18nService.translate('validation.common.tapToCopy', { lang: language_key });

        await ctx.replyWithHTML(
            `${textlogin}` +
                `${formatText(solText, solana.balance, solana.walletAddress)}` +
                `${formatText(ethText, evm.balance, evm.walletAddress)}` +
                `${formatText(arbText, arb.balance, arb.walletAddress)}` +
                `${refLink}` +
                `<a href="${domain ? `https://${domain}/` : appConfig.LANDING_PAGE_URL}">${domain ? `https://${domain}/` : appConfig.LANDING_PAGE_URL}</a> ${tapToCopy}\n`,
            Markup.inlineKeyboard([Markup.button.url('👉 Login Website', urlLogin)]),
        );
    }

    @Action('button1')
    act1(ctx: Context) {
        return `Hey 1`;
    }

    @Hears(['hi', 'hello', 'hey', 'qq'])
    onGreetings(@Sender('first_name') firstName: string): string {
        return `Hey ${firstName}`;
    }
}

function escapeMarkdownV2(text: string): string {
    return text.replace(/[_*[\]()~`>#+\-=|{}.!]/g, '\\$&');
}
