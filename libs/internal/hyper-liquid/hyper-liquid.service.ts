import { Injectable, OnModuleInit } from '@nestjs/common';
import { WalletService } from '../wallet/wallet.service';
import { ChainType, UserManagedWallet } from '../users/entities/user-managed-wallet.entity';
import { keccak256, TypedDataEncoder, verifyMessage, Wallet } from 'ethers';
import { UsersService } from '../users/users.service';
import { ApiError } from 'libs/common/api-errors';
import {
    AGENT_OF_WALLET_NOT_AVAILABLE,
    FAILED_TO_APPROVE_AGENT,
    GRPC_EXCEPTION,
    USER_NOT_ALLOW,
    USER_NOT_FOUND,
    USER_NOT_FOUND_ERROR,
    WALLET_NOT_FOUND,
} from 'libs/common/api-errors/errors';
import { HyperLiquidSdkService } from './hyper-liquid-sdk.service';
import { AuthProvider, User } from '../users/entities/user.entity';
import { InputPerpetualStatusDTO, PerpetualStatusDTO } from './dto/perpetual-status.dto';
import {
    CreateMasterWalletAgentRequest,
    CreateMasterWalletAgentResponse,
    MasterWalletResponse,
    ImportMasterWalletRequest,
    SignPlaceOrderRequest,
    SignPlaceOrderResponse,
    SignCancelOrderRequest,
    SignCancelOrderResponse,
} from 'protogen/ts/user/v1/hyper_liquid_vault';
import { VaultManagementService } from '@libs/internal/vault-management';
import { RpcException } from '@nestjs/microservices';
import { VaultMasterWallet } from '@libs/internal/vault-management/entities/vault-master-wallet.entity';
import { VaultAgentWallet } from '@libs/internal/vault-management/entities/vault-agent-wallet.entity';
import {
    HyperLiquidTransactionMessageDto,
    InputSignCancelOrderDTO,
    InputSignCreateOrderDTO,
    InputSignApproveAgentDTO,
    InputSignUpdateLeverageDTO,
    SignedCancelOrderDTO,
    SignedCreateOrderDTO,
    SignUpdateLeverageResponseDTO,
    SignatureDTO,
    InputSignApproveFeeBuilderDTO,
    InputSignApproveReferralDTO,
} from './dto/hyper-liquid-transaction.dto';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import {
    SignUserCancelOrderRequest,
    SignUserCancelOrderResponse,
    SignUserPlaceOrderRequest,
    SignUserPlaceOrderResponse,
} from '@protogen/user/v1/hyper_liquid_user';
import { PerpetualAgent } from '../users/entities/perpetual-agent.entity';
import { TurnkeyService } from '@lib/internal/turnkey';
import { IHyperLiquidConfig, loadSensitiveConfig, SENSITIVE_CONFIG } from 'libs/configs/sensitive';

@Injectable()
export class HyperLiquidService {
    private builderAddress: string;
    private referralCode: string;
    private maxFeeRate: number;

    private agentName = 'XBIT';

    constructor(
        private readonly walletService: WalletService,
        private readonly usersService: UsersService,
        private readonly hyperLiquidSdkService: HyperLiquidSdkService,
        private readonly vaultManagementService: VaultManagementService,
        private readonly turnkeyService: TurnkeyService,
        @InjectPinoLogger(HyperLiquidService.name)
        private readonly logger: PinoLogger,
    ) {
        const config = loadSensitiveConfig<IHyperLiquidConfig>(SENSITIVE_CONFIG.HYPER_LIQUID);
        this.builderAddress = config.builderAddress;
        this.referralCode = config.referralCode;
        this.maxFeeRate = config.maxFeeRate;
    }

    async importMasterWallet(input: ImportMasterWalletRequest): Promise<MasterWalletResponse> {
        try {
            const wallet = await this.walletService.encryptWallet(input.privateKey, ChainType.ARB);

            if (!wallet) {
                throw new RpcException(GRPC_EXCEPTION.INTERNAL_SERVER_ERROR);
            }

            const masterWallet = await this.vaultManagementService.createMasterWallet({
                address: wallet.address,
                encryptedPrivateKey: wallet.encrypt_private_key,
            });

            return {
                id: masterWallet.id,
                address: masterWallet.address,
            };
        } catch (error) {
            this.logger.error(error);
            throw new RpcException(GRPC_EXCEPTION.INTERNAL_SERVER_ERROR);
        }
    }

    async createVaultMasterWallet(): Promise<MasterWalletResponse> {
        const walletGenerator = await this.walletService.generateWallets([ChainType.ARB].join(','));

        if (!walletGenerator || walletGenerator?.length === 0) {
            throw new Error('Wallet generator is empty');
        }

        const masterWallet = await this.vaultManagementService.createMasterWallet({
            address: walletGenerator[0].address,
            encryptedPrivateKey: walletGenerator[0].encrypt_private_key,
        });

        if (!masterWallet) {
            throw new Error('Master wallet creation failed');
        }

        return {
            id: masterWallet.id,
            address: masterWallet.address,
        };
    }

    async createVaultAgentWallet(input: CreateMasterWalletAgentRequest): Promise<CreateMasterWalletAgentResponse> {
        const masterWallet = await this.vaultManagementService.getMasterWalletById(input.masterWalletId);

        if (!masterWallet) {
            throw new Error('Master wallet not found');
        }

        // const masterWalletBalance = await this.hyperLiquidSdkService.getBalance(masterWallet.address);
        // if (!masterWalletBalance) {
        //     throw new RpcException(GRPC_EXCEPTION.PLEASE_DEPOSIT_FIRST);
        // }

        // check agent wallet of masterWallet is existed
        const existedAgent = await this.vaultManagementService.getAgentWalletByMasterWallet(masterWallet.id);

        if (existedAgent) {
            if (existedAgent.expiredAt && existedAgent.expiredAt > Date.now() / 1000) {
                throw new RpcException(GRPC_EXCEPTION.AGENT_WALLET_ALREADY_EXISTS);
            }

            try {
                // try to re-approve agent
                const approval = await this.approveAgent(masterWallet, existedAgent.agentAddress);

                if (approval) {
                    return {
                        id: existedAgent.id,
                        address: existedAgent.agentAddress,
                        masterWalletId: masterWallet.id,
                    };
                }

                throw new RpcException(GRPC_EXCEPTION.FAILED_TO_APPROVE_AGENT);
            } catch (error) {
                this.logger.warn(error);
                throw new RpcException(GRPC_EXCEPTION.FAILED_TO_APPROVE_AGENT);
            }
        }

        const newAgentWallet = await this.walletService.generateWallets([ChainType.ARB].join(','));
        if (!newAgentWallet || newAgentWallet?.length === 0) {
            throw new RpcException(GRPC_EXCEPTION.FAILED_TO_GENERATE);
        }

        const approval = await this.approveAgent(masterWallet, newAgentWallet[0].address);

        if (!approval) {
            throw new RpcException(GRPC_EXCEPTION.FAILED_TO_APPROVE_AGENT);
        }

        const agentWallet = await this.vaultManagementService.createAgentWallet(masterWallet, {
            agentAddress: newAgentWallet[0].address,
            encryptedPrivateKey: newAgentWallet[0].encrypt_private_key,
            expiredAt: Math.floor(Date.now() / 1000) + 90 * 24 * 60 * 60,
        });

        if (!agentWallet) {
            throw new RpcException(GRPC_EXCEPTION.FAILED_TO_APPROVE_AGENT);
        }

        return {
            id: agentWallet.id,
            address: agentWallet.agentAddress,
            masterWalletId: masterWallet.id,
        };
    }

    async getUserPerpetualStatus(userId: string): Promise<PerpetualStatusDTO> {
        const user = await this.usersService.getUserById(userId);

        if (!user) {
            throw new ApiError(USER_NOT_FOUND_ERROR);
        }

        const status = await this.usersService.getPerpetualStatus(user);
        const setReferral = status ? status.setReferral : false,
            setFeeBuilder = status ? status.setFeeBuilder : false;

        const wallets = await this.usersService.getUserEmbeddedWallets(user);
        const arbWallet = wallets.find((w) => w.chain === ChainType.ARB);

        if (!arbWallet) {
            throw new ApiError(WALLET_NOT_FOUND);
        }

        let agent = await this.usersService.getPerpetualAgentByUser(user);
        if (!agent) {
            const walletGenerator = await this.walletService.generateWallets([ChainType.ARB].join(','));

            if (!walletGenerator || walletGenerator?.length === 0) {
                throw new Error('Wallet generator is empty');
            }

            agent = await this.usersService.createPerpetualAgent(user, {
                userAddress: arbWallet.walletAddress,
                agentAddress: walletGenerator[0].address,
                encryptedPrivateKey: walletGenerator[0].encrypt_private_key,
            });
        }

        const approvedAgent = status ? (agent.expiredAt ?? 0) > Date.now() / 1000 : false;
        if (approvedAgent && setReferral && setFeeBuilder) {
            return {
                approvedAgent,
                setReferral,
                setFeeBuilder,
                agentName: this.agentName,
                agent: agent.agentAddress,
                feeBuilderAddress: this.builderAddress,
                referralCode: this.referralCode,
                feeBuilderPercent: this.maxFeeRate,
            };
        }

        return {
            approvedAgent,
            setReferral,
            setFeeBuilder,
            agent: agent.agentAddress,
            agentName: this.agentName,
            feeBuilderAddress: this.builderAddress,
            referralCode: this.referralCode,
            feeBuilderPercent: this.maxFeeRate,
        };

        // if (!approvedAgent) {
        //     try {
        //         approvedAgent = await this.approveAgent(arbWallet, agent.agentAddress);
        //     } catch (e) {
        //         this.logger.warn(e);
        //     }
        // }

        // if (!setReferral) {
        //     try {
        //         setReferral = await this.approveReferral(arbWallet);
        //     } catch (e) {
        //         if (e.message === 'Referrer already set') {
        //             setReferral = true;
        //         }
        //         this.logger.warn(e);
        //     }
        // }

        // if (!setFeeBuilder) {
        //     try {
        //         setFeeBuilder = await this.approveFeeBuilder(arbWallet);
        //     } catch (e) {
        //         this.logger.warn(e);
        //     }
        // }

        // await this.usersService.updatePerpetualStatus(user, {
        //     setReferral,
        //     setFeeBuilder,
        //     referralCode: this.referralCode,
        //     feeBuilderAddress: this.builderAddress,
        //     feeBuilderPercent: this.maxFeeRate,
        // });
        // await this.usersService.updatePerpetualAgent(user, {
        //     expiredAt: approvedAgent ? Math.floor(Date.now() / 1000) + 90 * 24 * 60 * 60 : null,
        // });

        // return {
        //     approvedAgent,
        //     setReferral,
        //     setFeeBuilder,
        //     agent: agent.agentAddress,
        //     agentName: this.agentName,
        //     feeBuilderAddress: this.builderAddress,
        //     referralCode: this.referralCode,
        //     feeBuilderPercent: this.maxFeeRate,
        // };
    }

    async approveAgent(wallet: UserManagedWallet | VaultMasterWallet, agentAddress: string) {
        const message = this.hyperLiquidSdkService.createAddAgentMessage(agentAddress, this.agentName);

        return this.signAndSendTransaction(message, wallet);
    }

    async approveFeeBuilder(wallet: UserManagedWallet) {
        const message = this.hyperLiquidSdkService.createFeeBuilderMessage(this.builderAddress, this.maxFeeRate);

        return this.signAndSendTransaction(message, wallet);
    }

    async approveReferral(wallet: UserManagedWallet) {
        const message = this.hyperLiquidSdkService.createReferralMessage(this.referralCode);

        return this.signAndSendTransaction(message, wallet);
    }

    async signAndSendTransaction(
        message: HyperLiquidTransactionMessageDto,
        wallet: UserManagedWallet | VaultAgentWallet | VaultMasterWallet,
    ): Promise<true> {
        const signature = await this.signTransaction(message, wallet);
        const result = await this.hyperLiquidSdkService.postAction(message.action, signature, message.nonce);

        if (result.status == 'err') {
            throw new Error(result.response);
        }

        return true;
    }

    async signTransaction(
        message: HyperLiquidTransactionMessageDto,
        wallet: UserManagedWallet | VaultAgentWallet | VaultMasterWallet | PerpetualAgent,
    ): Promise<{ r: string; s: string; v: number }> {
        const signableData = TypedDataEncoder.encode(
            message.data['domain'],
            message.data['types'],
            message.data['message'],
        );
        const hashed = keccak256(signableData);

        const signature = await this.walletService.signHashed(wallet, hashed);

        return this.hyperLiquidSdkService.extractSignatureComponents(signature);
    }

    async approveHyperliquidTransaction(
        activityId: string,
        message: HyperLiquidTransactionMessageDto,
        user: User,
        chain: ChainType = ChainType.EVM,
    ) {
        const signableData = TypedDataEncoder.encode(
            message.data['domain'],
            message.data['types'],
            message.data['message'],
        );

        return this.turnkeyService.approveUserRawPayloadTransaction(activityId, user, chain, signableData);
    }

    async updateUserPerpetualStatus(userId: string, input: InputPerpetualStatusDTO) {
        const user = await this.usersService.getUserById(userId);

        if (!user) {
            throw new ApiError(USER_NOT_FOUND_ERROR);
        }

        if (user.authProvider === AuthProvider.TELEGRAM) {
            throw new ApiError(USER_NOT_ALLOW);
        }

        const [status, agent] = await Promise.all([
            this.usersService.updatePerpetualStatus(user, {
                setReferral: input.setReferral,
                setFeeBuilder: input.setFeeBuilder,
                referralCode: input.referralCode,
                feeBuilderAddress: input.feeBuilderAddress,
                feeBuilderPercent: input.feeBuilderPercent,
            }),
            this.usersService.updatePerpetualAgent(user, {
                expiredAt: input.agentExpiredAt,
            }),
        ]);

        return {
            approvedAgent: agent ? (agent.expiredAt ?? 0) > Date.now() / 1000 : false,
            setReferral: status.setReferral,
            setFeeBuilder: status.setFeeBuilder,
            agentName: this.agentName,
            agent: agent?.agentAddress,
            feeBuilderAddress: status.feeBuilderAddress,
            referralCode: status.referralCode,
            feeBuilderPercent: status.feeBuilderPercent,
        };
    }

    async signPlaceOrder(input: SignPlaceOrderRequest): Promise<SignPlaceOrderResponse> {
        const agentWallet = await this.vaultManagementService.getAgentWalletByMasterWallet(input.masterWalletId);

        if (!agentWallet || !agentWallet.expiredAt || agentWallet.expiredAt < Date.now() / 1000) {
            throw new RpcException(GRPC_EXCEPTION.AGENT_OF_WALLET_NOT_AVAILABLE);
        }

        const data = this.hyperLiquidSdkService.buildL1Action(
            input.action,
            input.vaultAddress,
            Number(input.nonce.toString()),
        );
        const signature = await this.signTransaction(
            {
                action: input.action,
                data,
                nonce: input.nonce,
                vaultAddress: input.vaultAddress,
            } as HyperLiquidTransactionMessageDto,
            agentWallet,
        );

        return {
            signature,
            action: input.action,
            nonce: input.nonce,
            vaultAddress: input.vaultAddress,
            masterWalletId: input.masterWalletId,
        };
    }

    async signCancelOrder(input: SignCancelOrderRequest): Promise<SignCancelOrderResponse> {
        const agentWallet = await this.vaultManagementService.getAgentWalletByMasterWallet(input.masterWalletId);

        if (!agentWallet || !agentWallet.expiredAt || agentWallet.expiredAt < Date.now() / 1000) {
            throw new RpcException(GRPC_EXCEPTION.AGENT_OF_WALLET_NOT_AVAILABLE);
        }

        const data = this.hyperLiquidSdkService.buildL1Action(
            input.action,
            input.vaultAddress,
            Number(input.nonce.toString()),
        );
        const signature = await this.signTransaction(
            {
                action: input.action,
                data,
                nonce: input.nonce,
                vaultAddress: input.vaultAddress,
            } as HyperLiquidTransactionMessageDto,
            agentWallet,
        );

        return {
            signature,
            action: input.action,
            nonce: input.nonce,
            vaultAddress: input.vaultAddress,
            masterWalletId: input.masterWalletId,
        };
    }

    async signUserPlaceOrder(input: SignUserPlaceOrderRequest): Promise<SignUserPlaceOrderResponse> {
        const user = await this.usersService.getUserById(input.userId);

        if (!user) {
            throw new RpcException(GRPC_EXCEPTION.USER_NOT_FOUND);
        }

        const agentWallet = await this.usersService.getPerpetualAgentByUser(user);

        if (!agentWallet || !agentWallet.expiredAt || agentWallet.expiredAt < Date.now() / 1000) {
            throw new RpcException(GRPC_EXCEPTION.AGENT_OF_WALLET_NOT_AVAILABLE);
        }

        const data = this.hyperLiquidSdkService.buildL1Action(
            input.action,
            input.vaultAddress,
            Number(input.nonce.toString()),
        );
        const signature = await this.signTransaction(
            {
                action: input.action,
                data,
                nonce: input.nonce,
                vaultAddress: input.vaultAddress,
            } as HyperLiquidTransactionMessageDto,
            agentWallet,
        );

        return {
            signature,
            action: input.action,
            nonce: input.nonce,
            vaultAddress: input.vaultAddress,
            userId: input.userId,
        };
    }

    async signUserPlaceOrderPublic(userId: string, input: InputSignCreateOrderDTO): Promise<SignedCreateOrderDTO> {
        const user = await this.usersService.getUserById(userId);

        if (!user) {
            throw new ApiError(USER_NOT_FOUND);
        }

        const agentWallet = await this.usersService.getPerpetualAgentByUser(user);

        if (!agentWallet || !agentWallet.expiredAt || agentWallet.expiredAt < Date.now() / 1000) {
            throw new ApiError(AGENT_OF_WALLET_NOT_AVAILABLE);
        }

        if (input.action.builder && input.action.builder.b) {
            input.action.builder.b = input.action.builder.b.toLowerCase();
        }

        const data = this.hyperLiquidSdkService.buildL1Action(
            input.action,
            input.vaultAddress,
            Number(input.nonce.toString()),
        );

        const signature = await this.signTransaction(
            {
                action: input.action,
                data,
                nonce: input.nonce,
                vaultAddress: input.vaultAddress,
            } as HyperLiquidTransactionMessageDto,
            agentWallet,
        );

        return {
            signature,
            userId: userId,
        };
    }

    async signUserCancelOrder(input: SignUserCancelOrderRequest): Promise<SignUserCancelOrderResponse> {
        const user = await this.usersService.getUserById(input.userId);

        if (!user) {
            throw new RpcException(GRPC_EXCEPTION.USER_NOT_FOUND);
        }

        const agentWallet = await this.usersService.getPerpetualAgentByUser(user);

        if (!agentWallet || !agentWallet.expiredAt || agentWallet.expiredAt < Date.now() / 1000) {
            throw new RpcException(GRPC_EXCEPTION.AGENT_OF_WALLET_NOT_AVAILABLE);
        }

        const data = this.hyperLiquidSdkService.buildL1Action(
            input.action,
            input.vaultAddress,
            Number(input.nonce.toString()),
        );
        const signature = await this.signTransaction(
            {
                action: input.action,
                data,
                nonce: input.nonce,
                vaultAddress: input.vaultAddress,
            } as HyperLiquidTransactionMessageDto,
            agentWallet,
        );

        return {
            signature,
            action: input.action,
            nonce: input.nonce,
            vaultAddress: input.vaultAddress,
            userId: input.userId,
        };
    }

    async signUserCancelOrderPublic(userId: string, input: InputSignCancelOrderDTO): Promise<SignedCancelOrderDTO> {
        const user = await this.usersService.getUserById(userId);

        if (!user) {
            throw new ApiError(USER_NOT_FOUND);
        }

        const agentWallet = await this.usersService.getPerpetualAgentByUser(user);

        if (!agentWallet || !agentWallet.expiredAt || agentWallet.expiredAt < Date.now() / 1000) {
            throw new ApiError(AGENT_OF_WALLET_NOT_AVAILABLE);
        }

        const data = this.hyperLiquidSdkService.buildL1Action(
            input.action,
            input.vaultAddress,
            Number(input.nonce.toString()),
        );
        const signature = await this.signTransaction(
            {
                action: input.action,
                data,
                nonce: input.nonce,
                vaultAddress: input.vaultAddress,
            } as HyperLiquidTransactionMessageDto,
            agentWallet,
        );

        return {
            signature,
            userId,
        };
    }

    async signUpdateLeverage(
        userId: string,
        input: InputSignUpdateLeverageDTO,
    ): Promise<SignUpdateLeverageResponseDTO> {
        const user = await this.usersService.getUserById(userId);

        if (!user) {
            throw new ApiError(USER_NOT_FOUND);
        }

        const agentWallet = await this.usersService.getPerpetualAgentByUser(user);

        if (!agentWallet || !agentWallet.expiredAt || agentWallet.expiredAt < Date.now() / 1000) {
            throw new ApiError(AGENT_OF_WALLET_NOT_AVAILABLE);
        }

        const data = this.hyperLiquidSdkService.buildL1Action(
            input.action,
            input.vaultAddress,
            Number(input.nonce.toString()),
        );
        const signature = await this.signTransaction(
            {
                action: input.action,
                data,
                nonce: input.nonce,
                vaultAddress: input.vaultAddress,
            } as HyperLiquidTransactionMessageDto,
            agentWallet,
        );

        return {
            signature,
            userId,
        };
    }

    async acceptApproveAgentActivity(userId: string, input: InputSignApproveAgentDTO): Promise<SignatureDTO> {
        const user = await this.usersService.getUserById(userId);

        if (!user) {
            throw new ApiError(USER_NOT_FOUND);
        }

        const message = this.hyperLiquidSdkService.createAddAgentMessage(
            input.agentAddress,
            input.agentName,
            input.nonce,
        );
        const res = await this.approveHyperliquidTransaction(input.activityId, message, user, ChainType.ARB);

        if (!res) {
            throw new ApiError(FAILED_TO_APPROVE_AGENT);
        }

        return {
            signature: res.signature,
            userId: userId,
        };
    }

    async acceptApproveFeeBuilderActivity(userId: string, input: InputSignApproveFeeBuilderDTO): Promise<SignatureDTO> {
        const user = await this.usersService.getUserById(userId);

        if (!user) {
            throw new ApiError(USER_NOT_FOUND);
        }

        const message = this.hyperLiquidSdkService.createFeeBuilderMessage(
            this.builderAddress,
            this.maxFeeRate,
            input.nonce,
        );
        const res = await this.approveHyperliquidTransaction(input.activityId, message, user, ChainType.ARB);

        if (!res) {
            throw new ApiError(FAILED_TO_APPROVE_AGENT);
        }

        return {
            signature: res.signature,
            userId: userId,
        };
    }

    async acceptApproveReferralActivity(userId: string, input: InputSignApproveReferralDTO): Promise<SignatureDTO> {
        const user = await this.usersService.getUserById(userId);

        if (!user) {
            throw new ApiError(USER_NOT_FOUND);
        }

        const message = this.hyperLiquidSdkService.createReferralMessage(this.referralCode, input.nonce);
        const res = await this.approveHyperliquidTransaction(input.activityId, message, user, ChainType.ARB);

        if (!res) {
            throw new ApiError(FAILED_TO_APPROVE_AGENT);
        }

        return {
            signature: res.signature,
            userId: userId,
        };
    }
}
