import { Entity, EntityRepositoryType, Enum, ManyToOne, PrimaryKey, Property, Unique } from '@mikro-orm/core';
import { Field, HideField, ObjectType, registerEnumType, Int } from '@nestjs/graphql';
import { User } from './user.entity';
import { uuidv7 } from 'uuidv7';
import { UserManagedWalletRepository } from '../repositories/user-managed-wallet.repository';

export enum ChainType {
    EVM = 'EVM',
    SOLANA = 'SOLANA',
    TRON = 'TRON',
    ARB = 'ARB',
}

registerEnumType(ChainType, {
    name: 'ChainType',
    description: 'Supported blockchain types',
});

@ObjectType()
@Entity({ repository: () => UserManagedWalletRepository })
@Unique({ properties: ['user', 'chain'] })
@Unique({ properties: ['walletAddress', 'chain'] })
export class UserManagedWallet {
    [EntityRepositoryType]?: UserManagedWalletRepository;

    @Field()
    @PrimaryKey({ type: 'uuid' })
    id: string = uuidv7();

    @ManyToOne(() => User)
    user: User;

    @Field(() => ChainType)
    @Enum({ items: () => ChainType, columnType: 'varchar(15)' })
    chain: ChainType;

    @Field()
    @Property()
    walletAddress: string;

    @HideField()
    @Property({ length: 511, lazy: true })
    encryptedPrivateKey: string;

    @Field(() => Int, { nullable: true })
    @Property({ type: 'integer', nullable: true })
    displayOrder?: number;

    @Property({ defaultRaw: 'now()' })
    createdAt: Date = new Date();
}
