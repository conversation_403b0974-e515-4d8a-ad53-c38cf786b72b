import { Injectable, Logger } from '@nestjs/common';
import { appConfig } from '../../configs';
import { EncryptedWalletResponseDto, WalletDTO, WalletResponseDTO } from './dto/wallet.dto';
import { BaseService } from '../base/base.service';
import { ChainType, UserManagedWallet } from '../users/entities/user-managed-wallet.entity';
import { VaultMasterWallet } from '@libs/internal/vault-management/entities/vault-master-wallet.entity';
import { VaultAgentWallet } from '@libs/internal/vault-management/entities/vault-agent-wallet.entity';
import { PerpetualAgent } from '../users/entities/perpetual-agent.entity';
import { Wallet } from 'ethers';
import { Keypair } from '@solana/web3.js';
import * as bs58 from 'bs58';
import { rsaEncrypt } from '../users/repositories/utils';

@Injectable()
export class WalletService {
    private readonly baseUrl = 'http://' + appConfig.WALLET_SERVICE_HOST + ':' + appConfig.WALLET_SERVICE_PORT;
    constructor(private readonly baseService: BaseService) {}

    public async generateWallets(chainTypes: string, userId?: string): Promise<WalletDTO[] | undefined> {
        // const configs = {
        //     timeout: 10000,
        //     headers: {
        //         'x-api-key': appConfig.WALLET_SERVICE_APIKEY,
        //     },
        // };
        // const data = {
        //     chain_types: chainTypes,
        // };
        // const url = this.baseUrl + '/api/wallet/v1/wallets';
        // const response = await this.baseService.call<WalletResponseDTO>('POST', url, data, configs);
        // if (response) {
        //     return response.data;
        // }
        const generatedWallets: WalletDTO[] = [];

        const chains = chainTypes.split(',').map((chain) => chain.trim());
        for (const chain of chains) {
            switch (chain) {
                case ChainType.ARB as string:
                case ChainType.EVM as string: {
                    const wallet = Wallet.createRandom();
                    const encryptedPrivateKey = rsaEncrypt(wallet.privateKey, userId ?? '');

                    generatedWallets.push({
                        address: wallet.address,
                        encrypt_private_key: encryptedPrivateKey,
                        chain_type: chain,
                    });

                    break;
                }
                case ChainType.SOLANA as string: {
                    const keypair = Keypair.generate();

                    const encryptedPrivateKey = rsaEncrypt(bs58.encode(keypair.secretKey), userId ?? '');

                    generatedWallets.push({
                        address: keypair.publicKey.toBase58(),
                        encrypt_private_key: encryptedPrivateKey,
                        chain_type: chain,
                    });

                    break;
                }
                default:
                    break;
            }
        }

        return generatedWallets;
    }

    public async signTransaction(wallet: UserManagedWallet, transaction: string): Promise<string | undefined> {
        const configs = {
            timeout: 10000,
            headers: {
                'x-api-key': appConfig.WALLET_SERVICE_APIKEY,
            },
        };
        const data = {
            chain_type: wallet.chain,
            encrypt_private_key: wallet.encryptedPrivateKey,
            unsigned_tx: transaction,
        };

        const url = this.baseUrl + '/api/wallet/v1/transactions/sign';
        const response = await this.baseService.call<{ data: string }>('POST', url, data, configs);

        if (response) {
            return response.data;
        }

        return undefined;
    }

    public async signHashed(
        wallet: UserManagedWallet | VaultMasterWallet | VaultAgentWallet | PerpetualAgent,
        transaction: string,
    ): Promise<string> {
        let chainType = ChainType.ARB; // default ARB for hyperliquid
        const configs = {
            timeout: 10000,
            headers: {
                'x-api-key': appConfig.WALLET_SERVICE_APIKEY,
            },
        };

        if (wallet instanceof UserManagedWallet) {
            chainType = wallet.chain;
        }

        const data = {
            chain_type: chainType,
            encrypt_private_key: wallet.encryptedPrivateKey,
            unsigned_tx: transaction,
        };

        const url = this.baseUrl + '/api/wallet/v1/hash/sign';
        const response = await this.baseService.call<{ data: string }>('POST', url, data, configs);

        if (response) {
            return response.data;
        }

        return '';
    }

    async encryptWallet(privateKey: string, chainType: string): Promise<WalletDTO | undefined> {
        const configs = {
            timeout: 10000,
            headers: {
                'x-api-key': appConfig.WALLET_SERVICE_APIKEY,
            },
        };

        const data = {
            chain_type: chainType,
            private_key: privateKey,
        };

        const url = this.baseUrl + '/api/wallet/v1/encrypt';
        const response = await this.baseService.call<EncryptedWalletResponseDto>('POST', url, data, configs);

        if (response) {
            return response.data;
        }

        return undefined;
    }
}
