import { TelegramUserDTO } from '../users/dto/telegram-user.dto';
import { UserEmbeddedWallet } from '../users/entities/user-embedded-wallet.entity';

const CACHE_PREFIX = 'user-service';

export const TTL_1_MINUTES = 60; // 1 minute
export const TTL_5_MINUTES = 5 * 60; // 5 minutes

export const CacheKey = {
    userSession: (userId: string) => `sniper-bot-session-setting:${userId}`,
    userInfo: (userDto: TelegramUserDTO) => `sniper-bot-user-info:${userDto.id}`,
    UserEmbeddedWallet: (userId: string) => `${CACHE_PREFIX}:wallet:${userId}`,
};
