import { Controller } from '@nestjs/common';
import {
    ApproveWithdrawRequest,
    ApproveWithdrawResponse,
    SignUserTransactionEvmRequest,
    SignUserTransactionEvmResponse,
    SignUserTransactionRequest,
    SignUserTransactionResponse,
    UserSigningServiceControllerMethods,
} from '@protogen/user/v1/signing';
import { UsersService } from 'libs/internal/users/users.service';
import { UserSigningService } from './user-signing.service';

@Controller()
@UserSigningServiceControllerMethods()
export class UserSigningController {
    constructor(
        private readonly usersService: UsersService,
        private readonly userSigningService: UserSigningService,
    ) {}

    async signEvmTransaction(request: SignUserTransactionEvmRequest): Promise<SignUserTransactionEvmResponse> {
        throw new Error('Method not implemented signEvmTransaction');
    }

    async signTransaction(request: SignUserTransactionRequest): Promise<SignUserTransactionResponse> {
        return this.userSigningService.signTransaction(request);
    }

    async approveWithdrawTransaction(request: ApproveWithdrawRequest): Promise<ApproveWithdrawResponse> {
        return this.userSigningService.approveWithdraw(request);
    }
}
